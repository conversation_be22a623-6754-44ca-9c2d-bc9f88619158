"""
Configuration loader for DataYatesV1 models.

This module provides functions for loading and saving model configurations
from YAML files, with validation and schema checking.
"""

import os
import yaml
from typing import Dict, Any, Optional, Union
from pathlib import Path

# Type aliases
ConfigDict = Dict[str, Any]

def load_config(config_path: Union[str, Path]) -> ConfigDict:
    """
    Load a model configuration from a YAML file.
    
    Args:
        config_path: Path to the YAML configuration file
        
    Returns:
        Dictionary containing the model configuration
        
    Raises:
        FileNotFoundError: If the config file doesn't exist
        yaml.YAMLError: If the YAML file is invalid
    """
    config_path = Path(config_path)
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
        
    with open(config_path, 'r') as f:
        try:
            config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Error parsing config file {config_path}: {e}")
            
    # Validate the loaded config
    validate_config(config)
    
    return config

def save_config(config: ConfigDict, config_path: Union[str, Path], overwrite: bool = False) -> None:
    """
    Save a model configuration to a YAML file.
    
    Args:
        config: Configuration dictionary to save
        config_path: Path where to save the YAML file
        overwrite: Whether to overwrite if file exists
        
    Raises:
        FileExistsError: If the file exists and overwrite=False
        yaml.YAMLError: If the config can't be serialized to YAML
    """
    config_path = Path(config_path)
    
    # Check if file exists and overwrite flag
    if config_path.exists() and not overwrite:
        raise FileExistsError(f"Config file already exists: {config_path}")
        
    # Create parent directories if they don't exist
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Validate before saving
    validate_config(config)
    
    with open(config_path, 'w') as f:
        try:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Error saving config to {config_path}: {e}")

def validate_config(config: ConfigDict) -> None:
    """
    Validate a model configuration dictionary.
    
    Args:
        config: Configuration dictionary to validate
        
    Raises:
        ValueError: If the configuration is invalid
    """
    # Required top-level keys
    required_keys = {'model_type'}
    
    # Check for required keys
    missing_keys = required_keys - set(config.keys())
    if missing_keys:
        raise ValueError(f"Missing required configuration keys: {missing_keys}")
    
    # Validate model type
    if config['model_type'] not in {'v1'}:  # Add more model types as needed
        raise ValueError(f"Unknown model type: {config['model_type']}")
    
    # Component-specific validation
    for component in ['frontend', 'convnet', 'recurrent', 'readout']:
        if component in config:
            validate_component_config(component, config[component])

def validate_component_config(component_name: str, component_config: ConfigDict) -> None:
    """
    Validate a component configuration dictionary.
    
    Args:
        component_name: Name of the component ('frontend', 'convnet', etc.)
        component_config: Configuration dictionary for the component
        
    Raises:
        ValueError: If the component configuration is invalid
    """
    # Required keys for each component
    required_keys = {'type', 'params'}
    
    # Check for required keys
    missing_keys = required_keys - set(component_config.keys())
    if missing_keys:
        raise ValueError(f"Missing required keys for {component_name}: {missing_keys}")
    
    # Validate component types
    valid_types = {
        'frontend': {'da', 'conv', 'temporal_basis', 'none'},
        'convnet': {'densenet', 'conv', 'resnet', 'none'},
        'recurrent': {'convlstm', 'convgru', 'none'},
        'readout': {'gaussian', 'gaussianei', 'linear'}
    }
    
    if component_name in valid_types:
        if component_config['type'] not in valid_types[component_name]:
            raise ValueError(
                f"Invalid {component_name} type: {component_config['type']}. "
                f"Must be one of: {valid_types[component_name]}"
            ) 